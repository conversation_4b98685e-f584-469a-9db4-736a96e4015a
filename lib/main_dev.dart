import 'dart:async';
import 'dart:developer';

import 'package:eljunto/constants/app_config.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/controller/app_version_controller.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/controller/inapp_purchase_controller.dart';
import 'package:eljunto/controller/leader_admin_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:eljunto/firebase_options_dev.dart';
import 'package:eljunto/reusableWidgets/custom_text_widget.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:eljunto/reusable_api_function/home/<USER>';
import 'package:eljunto/services/setup_locator.dart';
import 'package:eljunto/views/clubs/clubs_home/providers/clubs_home_provider.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:eljunto/views/local_database.dart';
import 'package:eljunto/views/routing.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'controller/login_controller.dart';
import 'controller/notification_controller.dart';
import 'controller/search_controller.dart';
import 'services/notification_service.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  NotificationServices().notificationCount++;
  NotificationServices().initializeBadgeCount();
  log("Message Id : ${message.messageId}");
  log("Message Title : ${message.data['title']}");
  log("Message Body : ${message.data['body']}");
  // Check if the notification contains deep link data
  if (message.data.containsKey('deepLink')) {
    try {
      final deepLinkUri = Uri.parse(message.data['deepLink']);
      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.setString('pendingDeepLink', deepLinkUri.toString());
    } catch (e) {
      log("Error parsing deep link: $e");
    }
  }
}

void main() async {
  AppConfig.create(
    appName: "El Junto Dev",
    baseUrl: Config.baseUrlDev,
    flavor: Flavor.dev,
  );

  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(
    name: "eljunto-dev",
    options: DevFirebaseOptions.currentPlatform,
  );
  setupLocator();
  DatabaseHelper.instance;
  NotificationServices().initializeBadgeCount();

  //  FOR HANDLING THE NOTIFICATION IN TERMINATED STATE
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => UserCredentialController(),
        ),
        ChangeNotifierProvider(
          create: (context) => LoginController(),
        ),
        ChangeNotifierProvider(
          create: (context) => UserController(),
        ),
        ChangeNotifierProvider(
          create: (context) => BookClubController(),
        ),
        ChangeNotifierProvider(
          create: (context) => ProfileController(),
        ),
        ChangeNotifierProvider(
          create: (context) => BookCaseController(),
        ),
        ChangeNotifierProvider(
          create: (context) => LeaderAdminController(),
        ),
        ChangeNotifierProvider(
          create: (context) => ClubController(),
        ),
        ChangeNotifierProvider(
          create: (context) => HomeController(),
        ),
        ChangeNotifierProvider(
          create: (context) => SearchDataController(),
        ),
        ChangeNotifierProvider(
          create: (context) => MessageController(),
        ),
        ChangeNotifierProvider(
          create: (context) => NotificationController(),
        ),
        ChangeNotifierProvider(
          create: (context) => SubscriptionController(),
        ),
        ChangeNotifierProvider(
          create: (context) => InAppPurchaseController(),
        ),
        ChangeNotifierProvider<ConnectivityProvider>.value(
          value: locator<ConnectivityProvider>(),
        ),
        ChangeNotifierProvider(
          create: (context) => AppVersionProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => UserClubDetailsProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => ClubsHomeProvider(),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String? fcmToken;
  late final NotificationServices _notificationServices;
  @override
  void initState() {
    final routeVariable =
        AppRouter.router.routerDelegate.currentConfiguration.fullPath;
    log("Route Variable : $routeVariable");
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Initialize app state
      AppRouter.appInitialized = true;

      // Check for any pending deep links and handle them only once
      final pendingLink = await AppRouter.getPendingRouteAndClear();
      if (pendingLink != null && mounted) {
        // Small delay to ensure the app is fully initialized
        Future.delayed(Duration(milliseconds: 300), () {
          _notificationServices.handleDeepLink(pendingLink);
        });
      }
    });
    super.initState();
    _notificationServices = NotificationServices();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    _notificationServices.requestNotificationPermission();

    SharedPreferences prefs = await SharedPreferences.getInstance();
    final jwtToken = prefs.getString('jwttoken');
    log('JWT Token : $jwtToken');
    if (jwtToken != null) {
      if (mounted) {
        _notificationServices.initLocalNotifications(context);
      }
      await _notificationServices.forgroundMessage();

      if (mounted) {
        _notificationServices.firebaseInit(context);
        _notificationServices.setupInteractMessage(context);
      }
    } else {
      // _notificationServices.setupInteractMessage(context);
    }
  }

  @override
  void dispose() {
    _notificationServices.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // _appLinksDeepLink.initDeepLinks();

    SystemChrome.setPreferredOrientations(
      [
        // DeviceOrientation.portraitUp,
        // DeviceOrientation.portraitDown,
      ],
    );
    return MaterialApp.router(
      // color: Colors.white,
      debugShowCheckedModeBanner: false,
      builder: (context, child) => MediaQuery(
        data: MediaQuery.of(context).copyWith(
          textScaler: const TextScaler.linear(1),
        ),
        child: Overlay(
          initialEntries: [
            OverlayEntry(
              builder: (context) => child!,
            ),
          ],
        ),
      ),
      title: 'El Junto',
      theme: ThemeData(
        inputDecorationTheme: InputDecorationTheme(
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(10.0),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(10.0),
          ),
          border: OutlineInputBorder(
            borderSide: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(10.0),
          ),
          labelStyle: libreBaskervilleRegular,
          errorStyle: errorMsg,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 20.0,
            horizontal: 10.0,
          ),
        ),
        textSelectionTheme: const TextSelectionThemeData(
          cursorColor: AppConstants.primaryColor,
          selectionColor: AppConstants.textGreenColor,
          selectionHandleColor: AppConstants.textGreenColor,
        ),
      ),
      routerConfig: AppRouter.router,
    );
  }
}
