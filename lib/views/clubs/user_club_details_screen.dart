import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/club_header_section.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/club_members_section.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/previous_meetings_section.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/upcoming_meetings_section.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Refactored User Club Details Screen
/// Uses Provider pattern with targeted refresh methods and proper separation of concerns
class UserClubDetailsScreen extends StatefulWidget {
  final int? bookClubId;
  final bool? fromChat;
  final String? userId;

  const UserClubDetailsScreen({
    super.key,
    this.bookClubId,
    this.fromChat,
    this.userId,
  });

  @override
  State<UserClubDetailsScreen> createState() => _UserClubDetailsScreenState();
}

class _UserClubDetailsScreenState extends State<UserClubDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider =
          Provider.of<UserClubDetailsProvider>(context, listen: false);
      provider.initialize(context);
      provider.loadInitialData(widget.bookClubId ?? 0, context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          appBar: _buildAppBar(provider),
          body: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  AppConstants.bgImagePath,
                ),
                filterQuality: FilterQuality.high,
                fit: BoxFit.fitWidth,
              ),
            ),
            child: Column(
              children: [
                Expanded(
                  child: RefreshIndicator(
                    color: AppConstants.primaryColor,
                    onRefresh: () => _handleRefresh(provider),
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Skeletonizer(
                        effect: const SoldColorEffect(
                          color: AppConstants.skeletonforgroundColor,
                          lowerBound: 0.1,
                          upperBound: 0.5,
                        ),
                        containersColor: AppConstants.skeletonBackgroundColor,
                        enabled: provider.isInitialLoading,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 25),

                            // Club Header Section
                            ClubHeaderSection(
                                bookClubId: widget.bookClubId ?? 0),

                            const SizedBox(height: 25),

                            // Club Members Section
                            ClubMembersSection(
                                bookClubId: widget.bookClubId ?? 0),

                            const SizedBox(height: 25),

                            // Upcoming Meetings Section
                            UpcomingMeetingsSection(
                                bookClubId: widget.bookClubId ?? 0),

                            const SizedBox(height: 25),

                            // Previous Meetings Section
                            PreviousMeetingsSection(
                                bookClubId: widget.bookClubId ?? 0),

                            const SizedBox(height: 25),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build app bar with club name and navigation
  PreferredSizeWidget _buildAppBar(UserClubDetailsProvider provider) {
    final bookClubName = provider.bookClubModel?.bookClubName ?? '';
    final clubCount = provider.bookClubModel?.clubCount ?? '';

    return PreferredSize(
      preferredSize: const Size.fromHeight(80),
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1.5,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        child: Consumer<BookClubController>(
          builder: (context, bookClubController, child) {
            return AppBar(
              backgroundColor: AppConstants.textGreenColor,
              centerTitle: true,
              title: Padding(
                padding: const EdgeInsets.only(top: 15.0),
                child: Column(
                  children: [
                    if (widget.fromChat ?? false) ...[
                      MarqueeList(
                        children: [
                          Text(
                            bookClubController.bookClubModel?.bookClubName ??
                                '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(fontSize: 18),
                          ),
                        ],
                      ),
                    ] else ...[
                      MarqueeList(
                        children: [
                          Text(
                            bookClubName,
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(fontSize: 18),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Text(
                        clubCount,
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(fontSize: 14),
                      ),
                    ],
                    const SizedBox.shrink(),
                  ],
                ),
              ),
              leading: Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 10),
                child: NetworkAwareTap(
                  onTap: () {
                    widget.fromChat ?? false
                        ? context.goNamed(
                            'chat-screen',
                            queryParameters: {
                              'userId': provider.loggedInUserId.toString(),
                              'bookClubId': widget.bookClubId.toString(),
                            },
                          )
                        : context.pop();
                  },
                  child: Image.asset(
                    width: 73,
                    height: 65,
                    "assets/icons/Back.png",
                    fit: BoxFit.contain,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 20.0, top: 10),
                  child: NetworkAwareTap(
                    onTap: () {
                      showDialog(
                        context: context,
                        barrierColor: Colors.white60,
                        builder: (BuildContext context) {
                          return const QuestionFeedbackDialog();
                        },
                      );
                    },
                    child: Image.asset(
                      AppConstants.questionLogoImagePath,
                      height: 34,
                      width: 34,
                    ),
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }

  /// Handle pull-to-refresh
  Future<void> _handleRefresh(UserClubDetailsProvider provider) async {
    await provider.loadInitialData(widget.bookClubId ?? 0, context);
  }
}
