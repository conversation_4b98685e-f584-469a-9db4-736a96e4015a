import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/meeting_card.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Widget for displaying previous meetings section
class PreviousMeetingsSection extends StatefulWidget {
  final int bookClubId;

  const PreviousMeetingsSection({
    super.key,
    required this.bookClubId,
  });

  @override
  State<PreviousMeetingsSection> createState() =>
      _PreviousMeetingsSectionState();
}

class _PreviousMeetingsSectionState extends State<PreviousMeetingsSection> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll for pagination
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      final provider =
          Provider.of<UserClubDetailsProvider>(context, listen: false);
      if (!provider.isPreviousMeetingsLoading &&
          (provider.previousMeetings?.length ?? 0) <
              provider.previousMeetingCount) {
        provider.getPreviousMeetings(widget.bookClubId, true, context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        final previousMeetings = provider.previousMeetings;
        final hasPreviousMeetings = previousMeetings?.isNotEmpty ?? false;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hasPreviousMeetings) ...[
              _buildSectionHeader(),
              const SizedBox(height: 10),
              _buildMeetingsList(provider),
            ] else ...[
              _buildNoDataWidget(provider),
            ],
          ],
        );
      },
    );
  }

  /// Build section header
  Widget _buildSectionHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Previous Meetings",
            style: lbRegular.copyWith(
              fontSize: 20,
              color: AppConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build horizontal scrollable meetings list
  Widget _buildMeetingsList(UserClubDetailsProvider provider) {
    final previousMeetings = provider.previousMeetings!;
    final isLoading = provider.isPreviousMeetingsLoading;

    return SizedBox(
      height: 195,
      child: ListView.builder(
        physics: const ClampingScrollPhysics(),
        controller: _scrollController,
        padding: const EdgeInsets.only(left: 10, right: 20),
        scrollDirection: Axis.horizontal,
        itemCount:
            isLoading ? previousMeetings.length + 1 : previousMeetings.length,
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index == previousMeetings.length && isLoading) {
            return const Padding(
              padding: EdgeInsets.only(left: 10.0),
              child: Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                ),
              ),
            );
          }

          final meeting = previousMeetings[index];
          return MeetingCard(
            meeting: meeting,
            meetingIndex: index,
            bookClubId: widget.bookClubId,
            isUpcoming: false,
          );
        },
      ),
    );
  }

  /// Build no data widget with skeleton loading
  Widget _buildNoDataWidget(UserClubDetailsProvider provider) {
    if (provider.isInitialLoading) {
      return Container(
        padding: const EdgeInsets.only(left: 20),
        margin: const EdgeInsets.symmetric(horizontal: 20),
        height: 50,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: AppConstants.skeletonBackgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: const Align(
          alignment: Alignment.centerLeft,
          child: Text(
            "Loading...",
            textAlign: TextAlign.start,
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 50,
      width: MediaQuery.of(context).size.width,
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Text(
          "No previous meetings",
          textAlign: TextAlign.start,
        ),
      ),
    );
  }
}
