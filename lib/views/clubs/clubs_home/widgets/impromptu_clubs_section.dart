import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/reusableWidgets/club_ui/impromptu_club_info.dart';
import 'package:eljunto/views/clubs/clubs_home/providers/clubs_home_provider.dart';
import 'package:eljunto/views/clubs/clubs_home/widgets/empty_state_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Widget for displaying impromptu clubs section with horizontal scrolling
class ImpromptuClubsSection extends StatefulWidget {
  const ImpromptuClubsSection({super.key});

  @override
  State<ImpromptuClubsSection> createState() => _ImpromptuClubsSectionState();
}

class _ImpromptuClubsSectionState extends State<ImpromptuClubsSection> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for pagination
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      final provider = Provider.of<ClubsHomeProvider>(context, listen: false);
      provider.loadMoreImpromptuClubs(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubsHomeProvider>(
      builder: (context, provider, child) {
        if (provider.hasImpromptuClubs) {
          return _buildClubsList(provider);
        } else {
          return _buildEmptyState(provider);
        }
      },
    );
  }

  /// Build the clubs list with horizontal scrolling
  Widget _buildClubsList(ClubsHomeProvider provider) {
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.only(left: 10, right: 20),
      child: Row(
        children: [
          // Club cards
          ...List.generate(
            provider.impromptuBookClubList?.length ?? 0,
            (index) {
              final club = provider.impromptuBookClubList![index];
              return Skeleton.replace(
                replacement: ImpromptuClubMeetingInfo(
                  bookClubModel: club,
                  loggedinUserId: provider.loggedInUserId,
                  isLoadingSkeleton: true,
                ),
                child: ImpromptuClubMeetingInfo(
                  bookClubModel: club,
                  loggedinUserId: provider.loggedInUserId,
                  userProfilePicture: provider.userProfilePicture,
                  userHandle: provider.userHandle,
                ),
              );
            },
          ),

          // Loading indicator for pagination
          if (provider.isImpromptuClubsLoading)
            const Padding(
              padding: EdgeInsets.only(left: 10.0),
              child: Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build empty state when no clubs are available
  Widget _buildEmptyState(ClubsHomeProvider provider) {
    return Skeleton.replace(
      replacement: EmptyStateWidget.skeleton(
        context: context,
        message: "Join or create an impromptu club",
      ),
      child: const EmptyStateWidget(
        message: "Join or create an impromptu club",
      ),
    );
  }
}
